"""
NQ 9:30 Entry Strategy

Entry: At 9:30, buy if 5-min bar is up, short if 5-min bar is down
Exit: Trailing stop using 30-bar rolling high/low, limit exit at close * 1.005/0.995
"""

from typing import Dict, Any, Optional, List
from datetime import datetime, time
import pandas as pd
from pytradelab.core.data_structures import Bar, Order, Trade
from pytradelab.core.enums import Side
from pytradelab.strategies.base_strategy import BaseStrategy


class NQ930Strategy(BaseStrategy):
    """
    NQ 9:30 Entry Strategy
    
    - Converts 1-minute data to 5-minute bars
    - Enters at 9:30 based on 5-min bar direction (up = long, down = short)
    - Trailing stop using 30-bar rolling high/low
    - Limit exit at close * 1.005 (long) / 0.995 (short)
    """
    
    def __init__(self, 
                 position_size: float = 10000.0,
                 trailing_bars: int = 30,
                 long_limit_multiplier: float = 1.005,
                 short_limit_multiplier: float = 0.995):
        """
        Initialize NQ 9:30 strategy
        
        Args:
            position_size: Position size in dollars
            trailing_bars: Number of bars for trailing stop calculation
            long_limit_multiplier: Multiplier for long limit exit
            short_limit_multiplier: Multiplier for short limit exit
        """
        super().__init__("NQ930Strategy")
        
        self.position_size = position_size
        self.trailing_bars = trailing_bars
        self.long_limit_multiplier = long_limit_multiplier
        self.short_limit_multiplier = short_limit_multiplier
        
        # Data storage for resampling
        self.minute_data: List[Dict] = []
        self.five_min_bars: List[Bar] = []
        self.last_processed_date: Optional[str] = None
        
        # Strategy state
        self.entry_price: Optional[float] = None
        self.trailing_stop: Optional[float] = None
        self.limit_exit: Optional[float] = None
        self.position_side: Optional[str] = None
        
        # Statistics
        self.signals_generated = 0
        self.trades_entered = 0
        
    def on_start(self) -> None:
        """Initialize strategy"""
        self.log(f"Starting NQ 9:30 strategy")
        self.log(f"Position size: ${self.position_size:,.2f}")
        self.log(f"Trailing stop bars: {self.trailing_bars}")
        self.log(f"Long limit multiplier: {self.long_limit_multiplier}")
        self.log(f"Short limit multiplier: {self.short_limit_multiplier}")
        self.is_initialized = True
        
    def on_bar(self, bar: Bar) -> None:
        """Process new 1-minute bar"""
        self.current_datetime = bar.timestamp

        # Store 1-minute bar data
        bar_data = {
            'timestamp': bar.timestamp,
            'open': bar.open,
            'high': bar.high,
            'low': bar.low,
            'close': bar.close,
            'volume': bar.volume
        }
        self.minute_data.append(bar_data)

        # Check if we need to process 5-minute bars (at end of trading day or significant data accumulation)
        current_date = bar.timestamp.strftime('%Y-%m-%d')

        # Process 5-minute bars at end of each trading day or when we have enough data
        if (self.last_processed_date != current_date and self.last_processed_date is not None) or len(self.minute_data) >= 1000:
            self._process_5min_bars()
            self.last_processed_date = current_date

        # Update trailing stops and check exits using current 1-minute bar
        if self.entry_price is not None:
            self._update_trailing_stop()
            self._check_exits(bar)
            
    def _process_5min_bars(self) -> None:
        """
        Process accumulated 1-minute data into 5-minute bars using pandas resampling
        """
        if len(self.minute_data) < 5:  # Need at least 5 minutes of data
            return

        # Convert to DataFrame
        df = pd.DataFrame(self.minute_data)
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df.set_index('timestamp', inplace=True)

        # Resample to 5-minute bars
        resampled = df.resample('5min').agg({
            'open': 'first',
            'high': 'max',
            'low': 'min',
            'close': 'last',
            'volume': 'sum'
        }).dropna()

        # Convert resampled data back to Bar objects and check for 9:30 entries
        for timestamp, row in resampled.iterrows():
            # Only process bars that haven't been processed yet
            if len(self.five_min_bars) == 0 or timestamp > self.five_min_bars[-1].timestamp:
                five_min_bar = Bar(
                    symbol="NQ",  # Assuming NQ symbol
                    timestamp=timestamp,
                    open=row['open'],
                    high=row['high'],
                    low=row['low'],
                    close=row['close'],
                    volume=row['volume']
                )

                self.five_min_bars.append(five_min_bar)

                # Check for 9:30 entry signal
                self._check_930_entry(five_min_bar)

        # Clear processed minute data to save memory (keep last few bars for continuity)
        if len(self.minute_data) > 100:
            self.minute_data = self.minute_data[-50:]
        
    def _check_930_entry(self, five_min_bar: Bar) -> None:
        """Check for 9:30 entry signal"""
        # Only enter if we don't have a position and it's 9:30
        if self.entry_price is not None:
            return
            
        bar_time = five_min_bar.timestamp.time()
        if bar_time != time(9, 30):
            return
            
        # Determine if bar is up or down
        is_up_bar = five_min_bar.close > five_min_bar.open
        
        if is_up_bar:
            self._enter_long_position(five_min_bar)
        else:
            self._enter_short_position(five_min_bar)
            
    def _enter_long_position(self, bar: Bar) -> None:
        """Enter long position"""
        quantity = self.position_size / bar.close
        self.buy_stop(bar.symbol, quantity)
        
        self.entry_price = bar.close
        self.position_side = "LONG"
        self.limit_exit = bar.close * self.long_limit_multiplier
        
        self.signals_generated += 1
        self.trades_entered += 1
        self.log(f"LONG entry at 9:30: ${self.entry_price:.2f}, Limit exit: ${self.limit_exit:.2f}")
        
    def _enter_short_position(self, bar: Bar) -> None:
        """Enter short position"""
        quantity = self.position_size / bar.close
        self.sell_market(bar.symbol, quantity)
        
        self.entry_price = bar.close
        self.position_side = "SHORT"
        self.limit_exit = bar.close * self.short_limit_multiplier
        
        self.signals_generated += 1
        self.trades_entered += 1
        self.log(f"SHORT entry at 9:30: ${self.entry_price:.2f}, Limit exit: ${self.limit_exit:.2f}")
        
    def _update_trailing_stop(self) -> None:
        """Update trailing stop based on 30-bar rolling high/low"""
        if len(self.five_min_bars) < self.trailing_bars:
            return
            
        # Get last 30 bars
        recent_bars = self.five_min_bars[-self.trailing_bars:]
        
        if self.position_side == "LONG":
            # Trailing stop is highest low of last 30 bars
            new_stop = min(bar.low for bar in recent_bars)
            if self.trailing_stop is None or new_stop > self.trailing_stop:
                self.trailing_stop = new_stop
        elif self.position_side == "SHORT":
            # Trailing stop is lowest high of last 30 bars
            new_stop = max(bar.high for bar in recent_bars)
            if self.trailing_stop is None or new_stop < self.trailing_stop:
                self.trailing_stop = new_stop
                
    def _check_exits(self, bar: Bar) -> None:
        """Check for exit conditions"""
        if self.entry_price is None:
            return
            
        position = self.get_position(bar.symbol)
        if not position or position.quantity == 0:
            return
            
        # Check limit exit
        if self.position_side == "LONG" and bar.high >= self.limit_exit:
            self.sell_market(bar.symbol, position.quantity)
            self.log(f"LONG limit exit hit at ${self.limit_exit:.2f}")
            self._reset_position()
            return
        elif self.position_side == "SHORT" and bar.low <= self.limit_exit:
            self.buy_market(bar.symbol, abs(position.quantity))
            self.log(f"SHORT limit exit hit at ${self.limit_exit:.2f}")
            self._reset_position()
            return
            
        # Check trailing stop
        if self.trailing_stop is not None:
            if self.position_side == "LONG" and bar.low <= self.trailing_stop:
                self.sell_market(bar.symbol, position.quantity)
                self.log(f"LONG trailing stop hit at ${self.trailing_stop:.2f}")
                self._reset_position()
            elif self.position_side == "SHORT" and bar.high >= self.trailing_stop:
                self.buy_market(bar.symbol, abs(position.quantity))
                self.log(f"SHORT trailing stop hit at ${self.trailing_stop:.2f}")
                self._reset_position()
                
    def _reset_position(self) -> None:
        """Reset position state"""
        self.entry_price = None
        self.trailing_stop = None
        self.limit_exit = None
        self.position_side = None
        
    def on_fill(self, order: Order, completed_trade: Trade = None) -> None:
        """Handle order fills"""
        if completed_trade:
            pnl_pct = (completed_trade.net_pnl / (completed_trade.entry_price * completed_trade.quantity)) * 100
            self.log(f"Trade completed: {completed_trade.side.value} "
                    f"P&L: ${completed_trade.net_pnl:.2f} ({pnl_pct:.2f}%)")
            
    def on_finish(self) -> None:
        """Finalize strategy"""
        # Process any remaining 5-minute bars
        if len(self.minute_data) > 0:
            self._process_5min_bars()

        self.log(f"Strategy finished. Signals generated: {self.signals_generated}")
        self.log(f"Trades entered: {self.trades_entered}")
        self.log(f"Total 5-minute bars processed: {len(self.five_min_bars)}")

        # Close any remaining positions
        if self.engine:
            for symbol in self.engine.data_handler.data_handlers.keys():
                position = self.get_position(symbol)
                if position and position.quantity != 0:
                    latest_bar = self.get_latest_bar(symbol)
                    if latest_bar:
                        if position.quantity > 0:
                            self.sell_market(symbol, position.quantity)
                        else:
                            self.buy_market(symbol, abs(position.quantity))
                        self.log(f"Closed remaining position in {symbol}")
