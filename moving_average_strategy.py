"""
Example Moving Average Crossover Strategy
"""

from typing import Dict, Any
from ..core.data_structures import Bar, Order, Trade
from ..core.enums import Side
from .base_strategy import BaseStrategy, TechnicalIndicators


class MovingAverageCrossoverStrategy(BaseStrategy):
    """
    Simple moving average crossover strategy
    
    Goes long when fast MA crosses above slow MA
    Goes short when fast MA crosses below slow MA
    """
    
    def __init__(self, 
                 fast_period: int = 10,
                 slow_period: int = 20,
                 position_size: float = 1000.0,
                 use_stops: bool = True,
                 stop_loss_pct: float = 0.02,
                 take_profit_pct: float = 0.04):
        """
        Initialize moving average crossover strategy
        
        Args:
            fast_period: Period for fast moving average
            slow_period: Period for slow moving average
            position_size: Position size in dollars
            use_stops: Whether to use stop losses and take profits
            stop_loss_pct: Stop loss percentage (0.02 = 2%)
            take_profit_pct: Take profit percentage (0.04 = 4%)
        """
        super().__init__("MovingAverageCrossover")
        
        self.fast_period = fast_period
        self.slow_period = slow_period
        self.position_size = position_size
        self.use_stops = use_stops
        self.stop_loss_pct = stop_loss_pct
        self.take_profit_pct = take_profit_pct
        
        # Strategy state
        self.prev_fast_ma = None
        self.prev_slow_ma = None
        self.entry_price = None
        self.stop_loss_price = None
        self.take_profit_price = None
        
        # Statistics
        self.signals_generated = 0
        self.trades_entered = 0
        
    def on_start(self) -> None:
        """Initialize strategy"""
        self.log(f"Starting strategy with fast_period={self.fast_period}, slow_period={self.slow_period}")
        self.log(f"Position size: ${self.position_size:,.2f}")
        if self.use_stops:
            self.log(f"Stop loss: {self.stop_loss_pct:.1%}, Take profit: {self.take_profit_pct:.1%}")
        
        self.is_initialized = True
        
    def on_bar(self, bar: Bar) -> None:
        """Process new bar"""
        self.current_datetime = bar.timestamp
        
        # Get historical bars for MA calculation
        bars = self.get_latest_bars(bar.symbol, self.slow_period)
        
        if len(bars) < self.slow_period:
            return  # Not enough data
            
        # Calculate moving averages
        fast_ma = TechnicalIndicators.sma(bars, self.fast_period)
        slow_ma = TechnicalIndicators.sma(bars, self.slow_period)
        
        if fast_ma is None or slow_ma is None:
            return
            
        # Check for crossover signals
        if self.prev_fast_ma is not None and self.prev_slow_ma is not None:
            self._check_entry_signals(bar, fast_ma, slow_ma)
            
        # Check stop loss and take profit
        if self.use_stops:
            self._check_exit_signals(bar)
            
        # Update previous values
        self.prev_fast_ma = fast_ma
        self.prev_slow_ma = slow_ma
        
    def _check_entry_signals(self, bar: Bar, fast_ma: float, slow_ma: float) -> None:
        """Check for entry signals"""
        position = self.get_position(bar.symbol)
        
        # Bullish crossover - fast MA crosses above slow MA
        if (self.prev_fast_ma <= self.prev_slow_ma and fast_ma > slow_ma):
            if self.is_flat(bar.symbol):
                self._enter_long_position(bar)
            elif self.is_short(bar.symbol):
                self._close_position(bar)
                self._enter_long_position(bar)
                
        # Bearish crossover - fast MA crosses below slow MA
        elif (self.prev_fast_ma >= self.prev_slow_ma and fast_ma < slow_ma):
            if self.is_flat(bar.symbol):
                self._enter_short_position(bar)
            elif self.is_long(bar.symbol):
                self._close_position(bar)
                self._enter_short_position(bar)
                
    def _enter_long_position(self, bar: Bar) -> None:
        """Enter long position"""
        quantity = self.position_size / bar.close
        self.buy_market(bar.symbol, quantity)
        
        self.entry_price = bar.close
        if self.use_stops:
            self.stop_loss_price = self.entry_price * (1 - self.stop_loss_pct)
            self.take_profit_price = self.entry_price * (1 + self.take_profit_pct)
            
        self.signals_generated += 1
        self.trades_entered += 1
        self.log(f"LONG signal: Entry at ${self.entry_price:.2f}")
        
    def _enter_short_position(self, bar: Bar) -> None:
        """Enter short position"""
        quantity = self.position_size / bar.close
        self.sell_market(bar.symbol, quantity)
        
        self.entry_price = bar.close
        if self.use_stops:
            self.stop_loss_price = self.entry_price * (1 + self.stop_loss_pct)
            self.take_profit_price = self.entry_price * (1 - self.take_profit_pct)
            
        self.signals_generated += 1
        self.trades_entered += 1
        self.log(f"SHORT signal: Entry at ${self.entry_price:.2f}")
        
    def _close_position(self, bar: Bar) -> None:
        """Close current position"""
        position = self.get_position(bar.symbol)
        if position and position.quantity != 0:
            if position.quantity > 0:
                self.sell_market(bar.symbol, position.quantity)
            else:
                self.buy_market(bar.symbol, abs(position.quantity))
                
    def _check_exit_signals(self, bar: Bar) -> None:
        """Check for stop loss and take profit exits"""
        if not self.use_stops or self.entry_price is None:
            return
            
        position = self.get_position(bar.symbol)
        if not position or position.quantity == 0:
            return
            
        # Check long position exits
        if position.quantity > 0:
            if bar.low <= self.stop_loss_price:
                self.sell_market(bar.symbol, position.quantity)
                self.log(f"STOP LOSS hit at ${self.stop_loss_price:.2f}")
                self._reset_stops()
            elif bar.high >= self.take_profit_price:
                self.sell_market(bar.symbol, position.quantity)
                self.log(f"TAKE PROFIT hit at ${self.take_profit_price:.2f}")
                self._reset_stops()
                
        # Check short position exits
        elif position.quantity < 0:
            if bar.high >= self.stop_loss_price:
                self.buy_market(bar.symbol, abs(position.quantity))
                self.log(f"STOP LOSS hit at ${self.stop_loss_price:.2f}")
                self._reset_stops()
            elif bar.low <= self.take_profit_price:
                self.buy_market(bar.symbol, abs(position.quantity))
                self.log(f"TAKE PROFIT hit at ${self.take_profit_price:.2f}")
                self._reset_stops()
                
    def _reset_stops(self) -> None:
        """Reset stop loss and take profit levels"""
        self.entry_price = None
        self.stop_loss_price = None
        self.take_profit_price = None
        
    def on_fill(self, order: Order, completed_trade: Trade = None) -> None:
        """Handle order fills"""
        if completed_trade:
            pnl_pct = (completed_trade.net_pnl / (completed_trade.entry_price * completed_trade.quantity)) * 100
            self.log(f"Trade completed: {completed_trade.side.value} "
                    f"P&L: ${completed_trade.net_pnl:.2f} ({pnl_pct:.2f}%)")
            
    def on_finish(self) -> None:
        """Finalize strategy"""
        self.log(f"Strategy finished. Signals generated: {self.signals_generated}")
        self.log(f"Trades entered: {self.trades_entered}")
        
        # Close any remaining positions
        if self.engine:
            for symbol in self.engine.data_handler.data_handlers.keys():
                position = self.get_position(symbol)
                if position and position.quantity != 0:
                    latest_bar = self.get_latest_bar(symbol)
                    if latest_bar:
                        self._close_position(latest_bar)
                        self.log(f"Closed remaining position in {symbol}")


class RSIMeanReversionStrategy(BaseStrategy):
    """
    RSI Mean Reversion Strategy
    
    Goes long when RSI is oversold (< 30)
    Goes short when RSI is overbought (> 70)
    """
    
    def __init__(self,
                 rsi_period: int = 14,
                 oversold_level: float = 30,
                 overbought_level: float = 70,
                 position_size: float = 1000.0):
        """
        Initialize RSI mean reversion strategy
        
        Args:
            rsi_period: Period for RSI calculation
            oversold_level: RSI level considered oversold
            overbought_level: RSI level considered overbought
            position_size: Position size in dollars
        """
        super().__init__("RSIMeanReversion")
        
        self.rsi_period = rsi_period
        self.oversold_level = oversold_level
        self.overbought_level = overbought_level
        self.position_size = position_size
        
        # Strategy state
        self.prev_rsi = None
        
    def on_start(self) -> None:
        """Initialize strategy"""
        self.log(f"Starting RSI strategy with period={self.rsi_period}")
        self.log(f"Oversold: {self.oversold_level}, Overbought: {self.overbought_level}")
        self.is_initialized = True
        
    def on_bar(self, bar: Bar) -> None:
        """Process new bar"""
        self.current_datetime = bar.timestamp
        
        # Get historical bars for RSI calculation
        bars = self.get_latest_bars(bar.symbol, self.rsi_period + 1)
        
        if len(bars) < self.rsi_period + 1:
            return
            
        # Calculate RSI
        rsi = TechnicalIndicators.rsi(bars, self.rsi_period)
        
        if rsi is None:
            return
            
        # Check for signals
        if self.prev_rsi is not None:
            self._check_signals(bar, rsi)
            
        self.prev_rsi = rsi
        
    def _check_signals(self, bar: Bar, rsi: float) -> None:
        """Check for RSI signals"""
        # Long signal - RSI crosses above oversold level
        if self.prev_rsi <= self.oversold_level and rsi > self.oversold_level:
            if self.is_flat(bar.symbol):
                quantity = self.position_size / bar.close
                self.buy_market(bar.symbol, quantity)
                self.log(f"RSI LONG signal: RSI={rsi:.1f}")
                
        # Short signal - RSI crosses below overbought level
        elif self.prev_rsi >= self.overbought_level and rsi < self.overbought_level:
            if self.is_flat(bar.symbol):
                quantity = self.position_size / bar.close
                self.sell_market(bar.symbol, quantity)
                self.log(f"RSI SHORT signal: RSI={rsi:.1f}")
                
        # Exit long when RSI becomes overbought
        elif rsi >= self.overbought_level and self.is_long(bar.symbol):
            position = self.get_position(bar.symbol)
            self.sell_market(bar.symbol, position.quantity)
            self.log(f"Exit LONG: RSI overbought at {rsi:.1f}")
            
        # Exit short when RSI becomes oversold
        elif rsi <= self.oversold_level and self.is_short(bar.symbol):
            position = self.get_position(bar.symbol)
            self.buy_market(bar.symbol, abs(position.quantity))
            self.log(f"Exit SHORT: RSI oversold at {rsi:.1f}")
            
    def on_finish(self) -> None:
        """Finalize strategy"""
        self.log("RSI strategy finished")
