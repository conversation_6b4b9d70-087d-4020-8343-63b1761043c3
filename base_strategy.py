"""
Base strategy class for implementing trading strategies
"""

from abc import ABC, abstractmethod
from typing import Optional, Dict, Any, TYPE_CHECKING
from datetime import datetime

from ..core.data_structures import Bar, Order, Trade, OrderEvent
from ..core.enums import Side, OrderType, EventType
from ..execution.order_manager import OrderFactory

if TYPE_CHECKING:
    from ..backtesting.engine import BacktestEngine


class BaseStrategy(ABC):
    """
    Abstract base class for trading strategies
    
    All trading strategies should inherit from this class and implement
    the required abstract methods.
    """
    
    def __init__(self, name: str = "BaseStrategy"):
        """
        Initialize base strategy
        
        Args:
            name: Name of the strategy
        """
        self.name = name
        self.engine: Optional['BacktestEngine'] = None
        self.parameters: Dict[str, Any] = {}
        
        # Strategy state
        self.is_initialized = False
        self.current_datetime: Optional[datetime] = None
        
    def set_engine(self, engine: 'BacktestEngine') -> None:
        """Set the backtesting engine reference"""
        self.engine = engine
        
    def set_parameters(self, parameters: Dict[str, Any]) -> None:
        """Set strategy parameters"""
        self.parameters.update(parameters)
        
    def get_parameter(self, key: str, default: Any = None) -> Any:
        """Get a strategy parameter"""
        return self.parameters.get(key, default)
        
    # Abstract methods that must be implemented by subclasses
    
    @abstractmethod
    def on_start(self) -> None:
        """
        Called when the backtest starts
        Initialize any strategy-specific variables here
        """
        pass
        
    @abstractmethod
    def on_bar(self, bar: Bar) -> None:
        """
        Called on each new bar
        
        Args:
            bar: New market data bar
        """
        pass
        
    @abstractmethod
    def on_finish(self) -> None:
        """
        Called when the backtest ends
        Cleanup and final calculations here
        """
        pass
        
    # Optional methods that can be overridden
    
    def on_fill(self, order: Order, completed_trade: Optional[Trade] = None) -> None:
        """
        Called when an order is filled
        
        Args:
            order: The filled order
            completed_trade: Completed trade if position was closed
        """
        pass
        
    def on_order_cancelled(self, order: Order) -> None:
        """
        Called when an order is cancelled
        
        Args:
            order: The cancelled order
        """
        pass
        
    # Helper methods for strategy implementation
    
    def buy_market(self, symbol: str, quantity: float) -> None:
        """
        Place a market buy order
        
        Args:
            symbol: Trading symbol
            quantity: Quantity to buy
        """
        if not self.engine:
            raise RuntimeError("Engine not set")
            
        order = OrderFactory.create_market_order(symbol, Side.LONG, quantity)
        order_event = OrderEvent(type=EventType.ORDER, timestamp=self.current_datetime, order=order)
        self.engine.place_order(order_event)
        
    def sell_market(self, symbol: str, quantity: float) -> None:
        """
        Place a market sell order
        
        Args:
            symbol: Trading symbol
            quantity: Quantity to sell
        """
        if not self.engine:
            raise RuntimeError("Engine not set")
            
        order = OrderFactory.create_market_order(symbol, Side.SHORT, quantity)
        order_event = OrderEvent(type=EventType.ORDER, timestamp=self.current_datetime, order=order)
        self.engine.place_order(order_event)
        
    def buy_limit(self, symbol: str, quantity: float, price: float) -> None:
        """
        Place a limit buy order
        
        Args:
            symbol: Trading symbol
            quantity: Quantity to buy
            price: Limit price
        """
        if not self.engine:
            raise RuntimeError("Engine not set")
            
        order = OrderFactory.create_limit_order(symbol, Side.LONG, quantity, price)
        order_event = OrderEvent(type=EventType.ORDER, timestamp=self.current_datetime, order=order)
        self.engine.place_order(order_event)
        
    def sell_limit(self, symbol: str, quantity: float, price: float) -> None:
        """
        Place a limit sell order
        
        Args:
            symbol: Trading symbol
            quantity: Quantity to sell
            price: Limit price
        """
        if not self.engine:
            raise RuntimeError("Engine not set")
            
        order = OrderFactory.create_limit_order(symbol, Side.SHORT, quantity, price)
        order_event = OrderEvent(type=EventType.ORDER, timestamp=self.current_datetime, order=order)
        self.engine.place_order(order_event)

    def buy_stop(self, symbol: str, quantity: float, stop_price: float) -> None:
        """
        Place a stop buy order

        Args:
            symbol: Trading symbol
            quantity: Quantity to buy
            stop_price: Stop price
        """
        if not self.engine:
            raise RuntimeError("Engine not set")

        order = OrderFactory.create_stop_order(symbol, Side.LONG, quantity, stop_price)
        order_event = OrderEvent(type=EventType.ORDER, timestamp=self.current_datetime, order=order)
        self.engine.place_order(order_event)

    def sell_stop(self, symbol: str, quantity: float, stop_price: float) -> None:
        """
        Place a stop sell order

        Args:
            symbol: Trading symbol
            quantity: Quantity to sell
            stop_price: Stop price
        """
        if not self.engine:
            raise RuntimeError("Engine not set")

        order = OrderFactory.create_stop_order(symbol, Side.SHORT, quantity, stop_price)
        order_event = OrderEvent(type=EventType.ORDER, timestamp=self.current_datetime, order=order)
        self.engine.place_order(order_event)
        
    def get_latest_bar(self, symbol: str) -> Optional[Bar]:
        """Get the latest bar for a symbol"""
        if not self.engine:
            return None
        return self.engine.get_latest_bar(symbol)
        
    def get_latest_bars(self, symbol: str, n: int = 1) -> list:
        """Get the latest n bars for a symbol"""
        if not self.engine:
            return []
        return self.engine.get_latest_bars(symbol, n)
        
    def get_position(self, symbol: str):
        """Get current position for a symbol"""
        if not self.engine:
            return None
        return self.engine.get_position(symbol)
        
    def get_cash(self) -> float:
        """Get current cash balance"""
        if not self.engine:
            return 0.0
        return self.engine.get_cash()
        
    def get_equity(self) -> float:
        """Get current total equity"""
        if not self.engine:
            return 0.0
        return self.engine.get_equity()
        
    def is_long(self, symbol: str) -> bool:
        """Check if currently long the symbol"""
        position = self.get_position(symbol)
        return position is not None and position.quantity > 0
        
    def is_short(self, symbol: str) -> bool:
        """Check if currently short the symbol"""
        position = self.get_position(symbol)
        return position is not None and position.quantity < 0
        
    def is_flat(self, symbol: str) -> bool:
        """Check if currently flat (no position) in the symbol"""
        position = self.get_position(symbol)
        return position is None or position.quantity == 0
        
    def log(self, message: str) -> None:
        """Log a message with timestamp"""
        timestamp_str = self.current_datetime.strftime("%Y-%m-%d %H:%M:%S") if self.current_datetime else "N/A"
        print(f"[{timestamp_str}] {self.name}: {message}")


class TechnicalIndicators:
    """
    Collection of common technical indicators for use in strategies
    """
    
    @staticmethod
    def sma(bars: list, period: int) -> Optional[float]:
        """
        Simple Moving Average
        
        Args:
            bars: List of bars (latest first)
            period: Period for the moving average
            
        Returns:
            SMA value or None if insufficient data
        """
        if len(bars) < period:
            return None
            
        closes = [bar.close for bar in bars[:period]]
        return sum(closes) / period
        
    @staticmethod
    def ema(bars: list, period: int) -> Optional[float]:
        """
        Exponential Moving Average
        
        Args:
            bars: List of bars (latest first)
            period: Period for the moving average
            
        Returns:
            EMA value or None if insufficient data
        """
        if len(bars) < period:
            return None
            
        multiplier = 2 / (period + 1)
        ema = bars[-1].close  # Start with oldest close
        
        for i in range(len(bars) - 2, -1, -1):
            ema = (bars[i].close * multiplier) + (ema * (1 - multiplier))
            
        return ema
        
    @staticmethod
    def rsi(bars: list, period: int = 14) -> Optional[float]:
        """
        Relative Strength Index
        
        Args:
            bars: List of bars (latest first)
            period: Period for RSI calculation
            
        Returns:
            RSI value or None if insufficient data
        """
        if len(bars) < period + 1:
            return None
            
        gains = []
        losses = []
        
        for i in range(period):
            change = bars[i].close - bars[i + 1].close
            if change > 0:
                gains.append(change)
                losses.append(0)
            else:
                gains.append(0)
                losses.append(abs(change))
                
        avg_gain = sum(gains) / period
        avg_loss = sum(losses) / period
        
        if avg_loss == 0:
            return 100
            
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        
        return rsi
