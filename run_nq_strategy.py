"""
Script to run NQ 9:30 strategy with your futures data
"""

import sys
import os
sys.path.insert(0, '../../../PyTradeLab')

from pytradelab import BacktestEngine
from pytradelab.data.data_handler import CSVDataHandler
from pytradelab.utils.export import TradeExporter
from nq_930_strategy import NQ930Strategy


def run_nq_930_backtest():
    """Run NQ 9:30 strategy backtest"""
    print("\n" + "="*60)
    print("RUNNING NQ 9:30 ENTRY STRATEGY BACKTEST")
    print("="*60)
    
    # Data file path
    data_file = r"C:\Users\<USER>\Desktop\Data1\Futures\1min\NQ.csv"
    
    if not os.path.exists(data_file):
        print(f"Error: Data file not found at {data_file}")
        return None, None
    
    # Initialize backtesting engine
    engine = BacktestEngine(
        initial_capital=100000.0,
        commission_rate=0.0001,  # 0.01% commission for futures
        slippage=0.25,           # 0.25 point slippage for NQ
        risk_free_rate=0.02      # 2% risk-free rate
    )
    
    # Set up data handler for your NQ data format
    data_handler = CSVDataHandler(
        symbol="NQ",
        csv_file=data_file,
        datetime_col='Date',
        open_col='Open',
        high_col='High',
        low_col='Low',
        close_col='Close',
        volume_col='Volume',
        datetime_format='%Y%m%d  %H:%M:%S'  # Format: 20181130  09:31:00
    )
    
    try:
        # Load data
        print("Loading NQ data...")
        data_handler.load_data()
        print(f"Loaded {len(data_handler.data)} bars")
        
        engine.add_data_handler("NQ", data_handler)
        
        # Create and configure strategy
        strategy = NQ930Strategy(
            position_size=50000.0,    # $50,000 per trade (adjust as needed)
            trailing_bars=30,         # 30 bars for trailing stop
            long_limit_multiplier=1.005,   # 0.5% profit target for longs
            short_limit_multiplier=0.995   # 0.5% profit target for shorts
        )
        
        engine.set_strategy(strategy)
        
        # Run backtest
        print("Running backtest...")
        results = engine.run_backtest()
        
        # Print results
        print(engine.get_results_summary())
        
        # Export results
        exporter = TradeExporter()
        exporter.print_performance_summary(results)
        
        # Create comprehensive report
        exporter.create_comprehensive_report(
            engine.portfolio_manager.trades,
            results,
            engine.portfolio_manager.equity_curve,
            "nq_930_backtest_results"
        )
        
        return engine, results
        
    except Exception as e:
        print(f"Error during backtesting: {e}")
        import traceback
        traceback.print_exc()
        return None, None


def analyze_results(engine, results):
    """Analyze and display additional results"""
    if engine is None or results is None:
        return
        
    print("\n" + "="*60)
    print("DETAILED ANALYSIS")
    print("="*60)
    
    all_results = results['all']
    
    print(f"Strategy Performance Summary:")
    print(f"- Total Trades: {all_results.total_trades}")
    print(f"- Win Rate: {all_results.win_rate:.2%}")
    print(f"- Net P&L: ${all_results.net_pnl:,.2f}")
    print(f"- Average Trade: ${all_results.avg_trade_pnl:.2f}")
    print(f"- Max Drawdown: {all_results.max_drawdown:.2%}")
    print(f"- Profit Factor: {all_results.profit_factor:.2f}")
    print(f"- Sharpe Ratio: {all_results.sharpe_ratio:.2f}")
    
    # Analyze trades by time
    trades = engine.portfolio_manager.trades
    if trades:
        print(f"\nTrade Analysis:")
        print(f"- First Trade: {trades[0].entry_timestamp}")
        print(f"- Last Trade: {trades[-1].exit_timestamp if trades[-1].exit_timestamp else 'Open'}")
        
        # Count winning vs losing trades
        winning_trades = [t for t in trades if t.net_pnl > 0]
        losing_trades = [t for t in trades if t.net_pnl < 0]
        
        if winning_trades:
            avg_win = sum(t.net_pnl for t in winning_trades) / len(winning_trades)
            print(f"- Average Winning Trade: ${avg_win:.2f}")
            
        if losing_trades:
            avg_loss = sum(t.net_pnl for t in losing_trades) / len(losing_trades)
            print(f"- Average Losing Trade: ${avg_loss:.2f}")


if __name__ == "__main__":
    print("NQ 9:30 Entry Strategy Backtester")
    print("=" * 60)
    
    try:
        # Run the backtest
        engine, results = run_nq_930_backtest()
        
        # Analyze results
        analyze_results(engine, results)
        
        print("\nBacktest completed!")
        print("Check the 'nq_930_backtest_results' folder for detailed CSV reports.")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
